<!DOCTYPE html>
<html lang="ar" dir="rtl" class="dark">
	<head>
		<title>{% block title %}نظام إدارة الموظفين{% endblock %}</title>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
		<link rel="preconnect" href="https://fonts.googleapis.com">
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
		<link href="https://fonts.googleapis.com/css2?family=Rubik:ital,wght@0,300..900;1,300..900&display=swap" rel="stylesheet">
		<script src="https://cdn.tailwindcss.com"></script>
		<script>
			tailwind.config = {
				darkMode: 'class',
				theme: {
					extend: {
						fontFamily: {
							'sans': ['Rubik', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', 'sans-serif'],
							'rubik': ['Rubik', 'sans-serif'],
						},
						colors: {
							primary: {
								50: '#eff6ff',
								100: '#dbeafe',
								200: '#bfdbfe',
								300: '#93c5fd',
								400: '#60a5fa',
								500: '#3b82f6',
								600: '#2563eb',
								700: '#1d4ed8',
								800: '#1e40af',
								900: '#1e3a8a',
							},
							dark: {
								50: '#f8fafc',
								100: '#f1f5f9',
								200: '#e2e8f0',
								300: '#cbd5e1',
								400: '#94a3b8',
								500: '#64748b',
								600: '#475569',
								700: '#334155',
								800: '#1e293b',
								900: '#0f172a',
							}
						},
						animation: {
							'fade-in': 'fadeIn 0.5s ease-in-out',
							'slide-up': 'slideUp 0.3s ease-out',
							'slide-down': 'slideDown 0.3s ease-out',
							'bounce-in': 'bounceIn 0.6s ease-out',
							'pulse-slow': 'pulse 3s infinite',
						},
						keyframes: {
							fadeIn: {
								'0%': { opacity: '0' },
								'100%': { opacity: '1' },
							},
							slideUp: {
								'0%': { transform: 'translateY(10px)', opacity: '0' },
								'100%': { transform: 'translateY(0)', opacity: '1' },
							},
							slideDown: {
								'0%': { transform: 'translateY(-10px)', opacity: '0' },
								'100%': { transform: 'translateY(0)', opacity: '1' },
							},
							bounceIn: {
								'0%': { transform: 'scale(0.3)', opacity: '0' },
								'50%': { transform: 'scale(1.05)' },
								'70%': { transform: 'scale(0.9)' },
								'100%': { transform: 'scale(1)', opacity: '1' },
							}
						},
						boxShadow: {
							'glow': '0 0 20px rgba(59, 130, 246, 0.5)',
							'glow-lg': '0 0 30px rgba(59, 130, 246, 0.6)',
							'dark-lg': '0 10px 25px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.1)',
						}
					}
				}
			}
		</script>
		<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
		<style>
			/* Custom scrollbar */
			::-webkit-scrollbar {
				width: 8px;
			}
			::-webkit-scrollbar-track {
				background: #f1f5f9;
			}
			.dark ::-webkit-scrollbar-track {
				background: #374151;
			}
			::-webkit-scrollbar-thumb {
				background: linear-gradient(180deg, #3b82f6, #1d4ed8);
				border-radius: 4px;
			}
			::-webkit-scrollbar-thumb:hover {
				background: linear-gradient(180deg, #2563eb, #1e40af);
			}

			/* Custom animations and effects */
			.glass-effect {
				backdrop-filter: blur(10px);
				background: rgba(255, 255, 255, 0.1);
				border: 1px solid rgba(255, 255, 255, 0.2);
			}

			.dark .glass-effect {
				background: rgba(0, 0, 0, 0.2);
				border: 1px solid rgba(255, 255, 255, 0.1);
			}

			.gradient-bg {
				background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			}

			.dark .gradient-bg {
				background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
			}

			/* Hover effects */
			.hover-lift {
				transition: all 0.3s ease;
			}
			.hover-lift:hover {
				transform: translateY(-2px);
				box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
			}

			.dark .hover-lift:hover {
				box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
			}

			/* Button glow effect */
			.btn-glow {
				position: relative;
				overflow: hidden;
			}
			.btn-glow::before {
				content: '';
				position: absolute;
				top: 0;
				left: -100%;
				width: 100%;
				height: 100%;
				background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
				transition: left 0.5s;
			}
			.btn-glow:hover::before {
				left: 100%;
			}

			/* Loading animation */
			.loading-dots::after {
				content: '';
				animation: loading 1.5s infinite;
			}
			@keyframes loading {
				0%, 20% { content: '.'; }
				40% { content: '..'; }
				60%, 100% { content: '...'; }
			}

			/* Floating animation */
			.float {
				animation: float 3s ease-in-out infinite;
			}
			@keyframes float {
				0%, 100% { transform: translateY(0px); }
				50% { transform: translateY(-10px); }
			}

			/* Pulse glow */
			.pulse-glow {
				animation: pulse-glow 2s infinite;
			}
			@keyframes pulse-glow {
				0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
				50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6); }
			}
		</style>
	</head>

	<body class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 text-gray-900 dark:text-gray-100 font-rubik leading-normal tracking-normal min-h-screen">
		<!-- Navigation -->
		<nav class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg shadow-lg border-b border-gray-200/50 dark:border-gray-700/50 sticky top-0 z-50">
			<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div class="flex justify-between h-16">
					<div class="flex items-center">
						<a href="{% url 'employees:employee_list' %}" class="flex items-center space-x-3 group">
							<div class="relative">
								<i class="fas fa-users text-primary-600 dark:text-primary-400 text-2xl group-hover:scale-110 transition-transform duration-300"></i>
								<div class="absolute -inset-1 bg-primary-600/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
							</div>
							<span class="text-xl font-bold bg-gradient-to-r from-primary-600 to-primary-800 dark:from-primary-400 dark:to-primary-600 bg-clip-text text-transparent">
								إدارة الموظفين
							</span>
						</a>
					</div>
					<div class="flex items-center space-x-2">
						<a href="{% url 'employees:employee_list' %}" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:bg-gray-100 dark:hover:bg-gray-700/50">
							<i class="fas fa-list ml-2"></i>جميع الموظفين
						</a>
						<a href="{% url 'employees:employee_create' %}" class="bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white px-6 py-2 rounded-lg text-sm font-medium transition-all duration-300 shadow-lg hover:shadow-xl btn-glow">
							<i class="fas fa-plus ml-2"></i>إضافة موظف
						</a>
						<button onclick="toggleDarkMode()" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:bg-gray-100 dark:hover:bg-gray-700/50">
							<i class="fas fa-moon dark:hidden text-lg"></i>
							<i class="fas fa-sun hidden dark:inline text-lg"></i>
						</button>
					</div>
				</div>
			</div>
		</nav>

		<!-- Main Content -->
		<main class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8 animate-fade-in">
			{% if messages %}
				<div class="mb-6">
					{% for message in messages %}
						<div class="alert alert-{{ message.tags }} animate-slide-down
							{% if message.tags == 'error' %}
								bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/50 dark:to-red-800/50 border border-red-200 dark:border-red-700 text-red-800 dark:text-red-200
							{% elif message.tags == 'success' %}
								bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/50 dark:to-green-800/50 border border-green-200 dark:border-green-700 text-green-800 dark:text-green-200
							{% elif message.tags == 'warning' %}
								bg-gradient-to-r from-yellow-50 to-yellow-100 dark:from-yellow-900/50 dark:to-yellow-800/50 border border-yellow-200 dark:border-yellow-700 text-yellow-800 dark:text-yellow-200
							{% else %}
								bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/50 dark:to-blue-800/50 border border-blue-200 dark:border-blue-700 text-blue-800 dark:text-blue-200
							{% endif %}
							px-6 py-4 rounded-xl shadow-lg backdrop-blur-sm mb-4" role="alert">
							<div class="flex items-center justify-between">
								<div class="flex items-center">
									<i class="fas fa-{% if message.tags == 'error' %}exclamation-circle{% elif message.tags == 'success' %}check-circle{% elif message.tags == 'warning' %}exclamation-triangle{% else %}info-circle{% endif %} mr-3 text-lg"></i>
									<span class="font-medium">{{ message }}</span>
								</div>
								<button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-current hover:text-opacity-75 transition-colors">
									<i class="fas fa-times"></i>
								</button>
							</div>
						</div>
					{% endfor %}
				</div>
			{% endif %}

			<div class="animate-slide-up">
				{% block content %}
				{% endblock %}
			</div>
		</main>

		<!-- Footer -->
		<footer class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg shadow-lg mt-auto border-t border-gray-200/50 dark:border-gray-700/50">
			<div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
				<div class="text-center">
					<div class="flex items-center justify-center space-x-2 mb-2">
						<i class="fas fa-heart text-red-500 animate-pulse"></i>
						<span class="text-gray-600 dark:text-gray-400 font-medium">تم البناء باستخدام</span>
						<i class="fab fa-python text-blue-500"></i>
						<span class="text-gray-600 dark:text-gray-400">Django و</span>
						<i class="fab fa-css3-alt text-blue-400"></i>
						<span class="text-gray-600 dark:text-gray-400">TailwindCSS</span>
					</div>
					<p class="text-sm text-gray-500 dark:text-gray-500">&copy; 2025 نظام إدارة الموظفين. جميع الحقوق محفوظة.</p>
				</div>
			</div>
		</footer>

		<script>
			// Dark mode toggle functionality
			function toggleDarkMode() {
				const html = document.documentElement;
				const isDark = html.classList.contains('dark');
				
				if (isDark) {
					html.classList.remove('dark');
					localStorage.setItem('darkMode', 'false');
				} else {
					html.classList.add('dark');
					localStorage.setItem('darkMode', 'true');
				}
			}

			// Initialize dark mode from localStorage
			document.addEventListener('DOMContentLoaded', function() {
				const darkMode = localStorage.getItem('darkMode');
				if (darkMode === 'false') {
					document.documentElement.classList.remove('dark');
				} else {
					document.documentElement.classList.add('dark');
				}
			});

			// Auto-hide alerts after 5 seconds
			document.addEventListener('DOMContentLoaded', function() {
				const alerts = document.querySelectorAll('.alert');
				alerts.forEach(alert => {
					setTimeout(() => {
						alert.style.transition = 'opacity 0.5s';
						alert.style.opacity = '0';
						setTimeout(() => alert.remove(), 500);
					}, 5000);
				});
			});
		</script>
	</body>
</html>
