from django.core.management.base import BaseCommand
from employees.models import Employee
from decimal import Decimal
from datetime import date, timedelta
import random


class Command(BaseCommand):
    help = 'Create sample employee data for demonstration'

    def handle(self, *args, **options):
        # Clear existing data
        Employee.objects.all().delete()
        
        # Sample data
        sample_employees = [
            {
                'first_name': '<PERSON>',
                'last_name': '<PERSON>',
                'email': '<EMAIL>',
                'phone_number': '******-0101',
                'employee_id': 'EMP001',
                'position': 'Software Engineer',
                'department': 'Engineering',
                'monthly_salary': Decimal('8500.00'),
                'manager_payment': Decimal('1200.00'),
                'employment_status': 'active',
                'hire_date': date(2022, 3, 15),
                'date_of_birth': date(1990, 5, 20),
                'address': '123 Main Street',
                'city': 'San Francisco',
                'state': 'CA',
                'postal_code': '94102',
                'country': 'USA'
            },
            {
                'first_name': '<PERSON>',
                'last_name': '<PERSON>',
                'email': '<EMAIL>',
                'phone_number': '******-0102',
                'employee_id': 'EMP002',
                'position': 'Product Manager',
                'department': 'Product',
                'monthly_salary': Decimal('9200.00'),
                'manager_payment': Decimal('1500.00'),
                'employment_status': 'active',
                'hire_date': date(2021, 8, 10),
                'date_of_birth': date(1988, 12, 3),
                'address': '456 Oak Avenue',
                'city': 'San Francisco',
                'state': 'CA',
                'postal_code': '94103',
                'country': 'USA'
            },
            {
                'first_name': 'Michael',
                'last_name': 'Brown',
                'email': '<EMAIL>',
                'phone_number': '******-0103',
                'employee_id': 'EMP003',
                'position': 'UX Designer',
                'department': 'Design',
                'monthly_salary': Decimal('7800.00'),
                'manager_payment': Decimal('1000.00'),
                'employment_status': 'active',
                'hire_date': date(2023, 1, 20),
                'date_of_birth': date(1992, 7, 15),
                'address': '789 Pine Street',
                'city': 'San Francisco',
                'state': 'CA',
                'postal_code': '94104',
                'country': 'USA'
            },
            {
                'first_name': 'Emily',
                'last_name': 'Davis',
                'email': '<EMAIL>',
                'phone_number': '******-0104',
                'employee_id': 'EMP004',
                'position': 'Data Scientist',
                'department': 'Engineering',
                'monthly_salary': Decimal('9500.00'),
                'manager_payment': Decimal('1400.00'),
                'employment_status': 'active',
                'hire_date': date(2022, 11, 5),
                'date_of_birth': date(1989, 3, 28),
                'address': '321 Elm Street',
                'city': 'San Francisco',
                'state': 'CA',
                'postal_code': '94105',
                'country': 'USA'
            },
            {
                'first_name': 'David',
                'last_name': 'Wilson',
                'email': '<EMAIL>',
                'phone_number': '******-0105',
                'employee_id': 'EMP005',
                'position': 'DevOps Engineer',
                'department': 'Engineering',
                'monthly_salary': Decimal('8800.00'),
                'manager_payment': Decimal('1300.00'),
                'employment_status': 'active',
                'hire_date': date(2023, 4, 12),
                'date_of_birth': date(1991, 9, 10),
                'address': '654 Maple Avenue',
                'city': 'San Francisco',
                'state': 'CA',
                'postal_code': '94106',
                'country': 'USA'
            },
            {
                'first_name': 'Lisa',
                'last_name': 'Anderson',
                'email': '<EMAIL>',
                'phone_number': '******-0106',
                'employee_id': 'EMP006',
                'position': 'Marketing Manager',
                'department': 'Marketing',
                'monthly_salary': Decimal('8200.00'),
                'manager_payment': Decimal('1100.00'),
                'employment_status': 'active',
                'hire_date': date(2022, 6, 18),
                'date_of_birth': date(1987, 11, 22),
                'address': '987 Cedar Street',
                'city': 'San Francisco',
                'state': 'CA',
                'postal_code': '94107',
                'country': 'USA'
            },
            {
                'first_name': 'Robert',
                'last_name': 'Taylor',
                'email': '<EMAIL>',
                'phone_number': '******-0107',
                'employee_id': 'EMP007',
                'position': 'Sales Representative',
                'department': 'Sales',
                'monthly_salary': Decimal('6500.00'),
                'manager_payment': Decimal('800.00'),
                'employment_status': 'inactive',
                'hire_date': date(2021, 12, 3),
                'date_of_birth': date(1993, 4, 8),
                'address': '147 Birch Lane',
                'city': 'San Francisco',
                'state': 'CA',
                'postal_code': '94108',
                'country': 'USA'
            },
            {
                'first_name': 'Jennifer',
                'last_name': 'Martinez',
                'email': '<EMAIL>',
                'phone_number': '******-0108',
                'employee_id': 'EMP008',
                'position': 'HR Manager',
                'department': 'Human Resources',
                'monthly_salary': Decimal('7500.00'),
                'manager_payment': Decimal('950.00'),
                'employment_status': 'active',
                'hire_date': date(2020, 9, 25),
                'date_of_birth': date(1985, 8, 14),
                'address': '258 Spruce Street',
                'city': 'San Francisco',
                'state': 'CA',
                'postal_code': '94109',
                'country': 'USA'
            }
        ]
        
        # Create employees
        created_count = 0
        for emp_data in sample_employees:
            employee = Employee.objects.create(**emp_data)
            created_count += 1
            self.stdout.write(f"Created employee: {employee.full_name}")
        
        self.stdout.write(
            self.style.SUCCESS(f'Successfully created {created_count} sample employees')
        )
