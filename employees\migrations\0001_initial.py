# Generated by Django 4.2.7 on 2025-07-31 12:29

from decimal import Decimal
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Employee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.CharField(max_length=100, verbose_name='First Name')),
                ('last_name', models.Char<PERSON>ield(max_length=100, verbose_name='Last Name')),
                ('email', models.EmailField(max_length=254, unique=True, verbose_name='Email Address')),
                ('phone_number', models.CharField(blank=True, max_length=20, verbose_name='Phone Number')),
                ('date_of_birth', models.DateField(blank=True, null=True, verbose_name='Date of Birth')),
                ('employee_id', models.<PERSON><PERSON><PERSON><PERSON>(max_length=20, unique=True, verbose_name='Employee ID')),
                ('position', models.CharField(max_length=100, verbose_name='Position/Job Title')),
                ('department', models.CharField(blank=True, max_length=100, verbose_name='Department')),
                ('hire_date', models.DateField(verbose_name='Hire Date')),
                ('address', models.TextField(blank=True, verbose_name='Address')),
                ('city', models.CharField(blank=True, max_length=100, verbose_name='City')),
                ('state', models.CharField(blank=True, max_length=100, verbose_name='State/Province')),
                ('postal_code', models.CharField(blank=True, max_length=20, verbose_name='Postal Code')),
                ('country', models.CharField(blank=True, max_length=100, verbose_name='Country')),
                ('monthly_salary', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='Monthly Salary')),
                ('manager_payment', models.DecimalField(decimal_places=2, help_text='Amount that the manager must pay the employee', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='Manager Payment Amount')),
                ('employment_status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('terminated', 'Terminated'), ('on_leave', 'On Leave')], default='active', max_length=20, verbose_name='Employment Status')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Employee',
                'verbose_name_plural': 'Employees',
                'ordering': ['last_name', 'first_name'],
            },
        ),
    ]
