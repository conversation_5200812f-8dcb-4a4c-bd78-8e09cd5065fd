{% extends 'base.html' %}

{% block title %}{{ employee.full_name }} - Employee Management System{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto">
    <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg shadow-2xl rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover-lift">
        <div class="px-8 py-6 border-b border-gray-200/50 dark:border-gray-700/50 bg-gradient-to-r from-primary-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-t-2xl">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-6">
                    <div class="relative">
                        <div class="h-20 w-20 rounded-full bg-gradient-to-br from-primary-500 to-primary-700 flex items-center justify-center shadow-xl">
                            <span class="text-white font-bold text-2xl">{{ employee.first_name.0 }}{{ employee.last_name.0 }}</span>
                        </div>
                        <div class="absolute -inset-2 bg-primary-500/20 rounded-full blur opacity-50 animate-pulse-slow"></div>
                    </div>
                    <div>
                        <h1 class="text-4xl font-bold bg-gradient-to-r from-primary-600 to-primary-800 dark:from-primary-400 dark:to-primary-600 bg-clip-text text-transparent">
                            {{ employee.full_name }}
                        </h1>
                        <p class="text-lg text-gray-600 dark:text-gray-400 mt-1">{{ employee.position }}</p>
                        <p class="text-sm text-gray-500 dark:text-gray-500 font-mono">ID: {{ employee.employee_id }}</p>
                    </div>
                </div>
                <div class="flex space-x-3 space-x-reverse">
                    <a href="{% url 'employees:employee_update' employee.pk %}" class="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 btn-glow">
                        <i class="fas fa-edit ml-2"></i>تعديل
                    </a>
                    <a href="{% url 'employees:employee_delete' employee.pk %}" class="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 btn-glow">
                        <i class="fas fa-trash ml-2"></i>حذف
                    </a>
                </div>
            </div>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Personal Information -->
                <div class="md:col-span-2">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4 border-b border-gray-200 dark:border-gray-700 pb-2">
                        <i class="fas fa-user-circle mr-2"></i>Personal Information
                    </h3>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">First Name</label>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ employee.first_name }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Last Name</label>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ employee.last_name }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Email</label>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">
                        <a href="mailto:{{ employee.email }}" class="text-blue-600 hover:text-blue-800 dark:text-blue-400">{{ employee.email }}</a>
                    </p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Phone Number</label>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ employee.phone_number|default:"-" }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Date of Birth</label>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ employee.date_of_birth|default:"-" }}</p>
                </div>

                <!-- Employment Information -->
                <div class="md:col-span-2 mt-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4 border-b border-gray-200 dark:border-gray-700 pb-2">
                        <i class="fas fa-briefcase mr-2"></i>Employment Information
                    </h3>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Employee ID</label>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white font-mono">{{ employee.employee_id }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Position</label>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ employee.position }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Department</label>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ employee.department|default:"-" }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Hire Date</label>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ employee.hire_date }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Employment Status</label>
                    <p class="mt-1">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                   {% if employee.employment_status == 'active' %}bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100
                                   {% elif employee.employment_status == 'inactive' %}bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100
                                   {% elif employee.employment_status == 'terminated' %}bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100
                                   {% else %}bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100{% endif %}">
                            {{ employee.get_employment_status_display }}
                        </span>
                    </p>
                </div>

                <!-- Address Information -->
                <div class="md:col-span-2 mt-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4 border-b border-gray-200 dark:border-gray-700 pb-2">
                        <i class="fas fa-map-marker-alt mr-2"></i>Address Information
                    </h3>
                </div>

                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Address</label>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ employee.address|default:"-" }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">City</label>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ employee.city|default:"-" }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">State/Province</label>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ employee.state|default:"-" }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Postal Code</label>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ employee.postal_code|default:"-" }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Country</label>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ employee.country|default:"-" }}</p>
                </div>

                <!-- Salary Information -->
                <div class="md:col-span-2 mt-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4 border-b border-gray-200 dark:border-gray-700 pb-2">
                        <i class="fas fa-dollar-sign mr-2"></i>Salary Information
                    </h3>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Monthly Salary</label>
                    <p class="mt-1 text-lg font-semibold text-green-600 dark:text-green-400">${{ employee.monthly_salary|floatformat:2 }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Manager Payment</label>
                    <p class="mt-1 text-lg font-semibold text-blue-600 dark:text-blue-400">${{ employee.manager_payment|floatformat:2 }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Annual Salary</label>
                    <p class="mt-1 text-lg font-semibold text-green-600 dark:text-green-400">${{ employee.annual_salary|floatformat:2 }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Annual Manager Payment</label>
                    <p class="mt-1 text-lg font-semibold text-blue-600 dark:text-blue-400">${{ employee.annual_manager_payment|floatformat:2 }}</p>
                </div>

                <!-- Timestamps -->
                <div class="md:col-span-2 mt-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4 border-b border-gray-200 dark:border-gray-700 pb-2">
                        <i class="fas fa-clock mr-2"></i>Record Information
                    </h3>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Created</label>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ employee.created_at }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Last Updated</label>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ employee.updated_at }}</p>
                </div>
            </div>

            <div class="mt-8 flex justify-end space-x-3">
                <a href="{% url 'employees:employee_list' %}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                    <i class="fas fa-arrow-left mr-1"></i>Back to List
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
