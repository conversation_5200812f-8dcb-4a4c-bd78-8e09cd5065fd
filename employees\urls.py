from django.urls import path
from . import views

app_name = 'employees'

urlpatterns = [
    # Dashboard
    path('', views.dashboard, name='dashboard'),

    # Employee URLs
    path('employees/', views.employee_list, name='employee_list'),
    path('employee/<int:pk>/', views.employee_detail, name='employee_detail'),
    path('employee/create/', views.employee_create, name='employee_create'),
    path('employee/<int:pk>/edit/', views.employee_update, name='employee_update'),
    path('employee/<int:pk>/delete/', views.employee_delete, name='employee_delete'),

    # Employee Services URLs
    path('services/', views.service_list, name='service_list'),
    path('service/<int:pk>/', views.service_detail, name='service_detail'),
    path('service/create/', views.service_create, name='service_create'),
    path('service/<int:pk>/edit/', views.service_update, name='service_update'),
    path('service/<int:pk>/delete/', views.service_delete, name='service_delete'),
]
