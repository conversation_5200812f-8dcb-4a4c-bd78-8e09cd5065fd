{% extends 'base.html' %}

{% block title %}{{ title }} - Employee Management System{% endblock %}

{% block content %}
<div class="max-w-5xl mx-auto">
    <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg shadow-2xl rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover-lift">
        <div class="px-8 py-6 border-b border-gray-200/50 dark:border-gray-700/50 bg-gradient-to-r from-primary-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-t-2xl">
            <div class="flex items-center space-x-4">
                <div class="relative">
                    <i class="fas fa-user-plus text-3xl text-primary-600 dark:text-primary-400 float"></i>
                    <div class="absolute -inset-2 bg-primary-600/20 rounded-full blur opacity-50"></div>
                </div>
                <div>
                    <h1 class="text-3xl font-bold bg-gradient-to-r from-primary-600 to-primary-800 dark:from-primary-400 dark:to-primary-600 bg-clip-text text-transparent">
                        {{ title }}
                    </h1>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">املأ معلومات الموظف أدناه</p>
                </div>
            </div>
        </div>

        <form method="post" class="p-8">
            {% csrf_token %}

            {% if form.non_field_errors %}
                <div class="mb-6 p-6 bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/50 dark:to-red-800/50 border border-red-200 dark:border-red-700 text-red-800 dark:text-red-200 rounded-xl shadow-lg backdrop-blur-sm animate-slide-down">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle mr-3 text-lg"></i>
                        <div>{{ form.non_field_errors }}</div>
                    </div>
                </div>
            {% endif %}

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Personal Information -->
                <div class="lg:col-span-2">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-8 h-8 bg-gradient-to-r from-primary-500 to-primary-700 rounded-lg flex items-center justify-center">
                            <i class="fas fa-user text-white"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white">المعلومات الشخصية</h3>
                    </div>
                </div>
                
                <div class="space-y-2">
                    <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-semibold text-gray-700 dark:text-gray-300">
                        <i class="fas fa-user mr-2 text-primary-500"></i>{{ form.first_name.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ form.first_name }}
                    {% if form.first_name.errors %}
                        <p class="mt-2 text-sm text-red-600 dark:text-red-400 flex items-center">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ form.first_name.errors.0 }}
                        </p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {{ form.last_name.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ form.last_name }}
                    {% if form.last_name.errors %}
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.last_name.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {{ form.email.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ form.email }}
                    {% if form.email.errors %}
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.email.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.phone_number.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {{ form.phone_number.label }}
                    </label>
                    {{ form.phone_number }}
                    {% if form.phone_number.errors %}
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.phone_number.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.date_of_birth.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {{ form.date_of_birth.label }}
                    </label>
                    {{ form.date_of_birth }}
                    {% if form.date_of_birth.errors %}
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.date_of_birth.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Employment Information -->
                <div class="md:col-span-2 mt-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Employment Information</h3>
                </div>

                <div>
                    <label for="{{ form.employee_id.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {{ form.employee_id.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ form.employee_id }}
                    {% if form.employee_id.errors %}
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.employee_id.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.position.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {{ form.position.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ form.position }}
                    {% if form.position.errors %}
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.position.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.department.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {{ form.department.label }}
                    </label>
                    {{ form.department }}
                    {% if form.department.errors %}
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.department.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.hire_date.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {{ form.hire_date.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ form.hire_date }}
                    {% if form.hire_date.errors %}
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.hire_date.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.employment_status.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {{ form.employment_status.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ form.employment_status }}
                    {% if form.employment_status.errors %}
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.employment_status.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Address Information -->
                <div class="md:col-span-2 mt-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Address Information</h3>
                </div>

                <div class="md:col-span-2">
                    <label for="{{ form.address.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {{ form.address.label }}
                    </label>
                    {{ form.address }}
                    {% if form.address.errors %}
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.address.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.city.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {{ form.city.label }}
                    </label>
                    {{ form.city }}
                    {% if form.city.errors %}
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.city.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.state.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {{ form.state.label }}
                    </label>
                    {{ form.state }}
                    {% if form.state.errors %}
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.state.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.postal_code.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {{ form.postal_code.label }}
                    </label>
                    {{ form.postal_code }}
                    {% if form.postal_code.errors %}
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.postal_code.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.country.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {{ form.country.label }}
                    </label>
                    {{ form.country }}
                    {% if form.country.errors %}
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.country.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Salary Information -->
                <div class="md:col-span-2 mt-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Salary Information</h3>
                </div>

                <div>
                    <label for="{{ form.monthly_salary.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {{ form.monthly_salary.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ form.monthly_salary }}
                    {% if form.monthly_salary.errors %}
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.monthly_salary.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.manager_payment.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {{ form.manager_payment.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ form.manager_payment }}
                    {% if form.manager_payment.errors %}
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.manager_payment.errors.0 }}</p>
                    {% endif %}
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ form.manager_payment.help_text }}</p>
                </div>
            </div>

            <div class="mt-12 flex justify-start space-x-4 space-x-reverse">
                <button type="submit" class="bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 btn-glow">
                    <i class="fas fa-save ml-2"></i>{{ submit_text }}
                </button>
                <a href="{% url 'employees:employee_list' %}" class="bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
                    <i class="fas fa-times ml-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
