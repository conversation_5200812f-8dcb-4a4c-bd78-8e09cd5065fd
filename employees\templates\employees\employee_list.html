{% extends 'base.html' %}

{% block title %}Employee List - Employee Management System{% endblock %}

{% block content %}
<div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg shadow-xl rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover-lift">
    <div class="px-8 py-6 border-b border-gray-200/50 dark:border-gray-700/50 bg-gradient-to-r from-primary-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-t-2xl">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <div class="relative">
                    <i class="fas fa-users text-3xl text-primary-600 dark:text-primary-400 float"></i>
                    <div class="absolute -inset-2 bg-primary-600/20 rounded-full blur opacity-50"></div>
                </div>
                <div>
                    <h1 class="text-3xl font-bold bg-gradient-to-r from-primary-600 to-primary-800 dark:from-primary-400 dark:to-primary-600 bg-clip-text text-transparent">
                        الموظفون
                    </h1>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">إدارة أعضاء فريقك</p>
                </div>
            </div>
            <div class="text-right">
                <div class="text-3xl font-bold text-primary-600 dark:text-primary-400">{{ total_employees }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">إجمالي الموظفين</div>
            </div>
        </div>
        
        <!-- Search Form -->
        <div class="mt-6">
            <form method="get" class="flex gap-4">
                <div class="flex-1 relative">
                    <div class="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400 dark:text-gray-500"></i>
                    </div>
                    <input type="text" name="search" value="{{ search_query }}"
                           placeholder="البحث بالاسم أو البريد الإلكتروني أو المنصب أو القسم..."
                           class="w-full pr-12 pl-4 py-3 rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400">
                </div>
                <button type="submit" class="bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 btn-glow">
                    <i class="fas fa-search ml-2"></i>بحث
                </button>
                {% if search_query %}
                <a href="{% url 'employees:employee_list' %}" class="bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
                    <i class="fas fa-times ml-2"></i>مسح
                </a>
                {% endif %}
            </form>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="min-w-full">
            <thead class="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600">
                <tr>
                    <th class="px-8 py-4 text-right text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">الموظف</th>
                    <th class="px-8 py-4 text-right text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">المنصب</th>
                    <th class="px-8 py-4 text-right text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">القسم</th>
                    <th class="px-8 py-4 text-right text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">الراتب الشهري</th>
                    <th class="px-8 py-4 text-right text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">دفعة المدير</th>
                    <th class="px-8 py-4 text-right text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">الحالة</th>
                    <th class="px-8 py-4 text-right text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">الإجراءات</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-100 dark:divide-gray-700/50">
                {% for employee in page_obj %}
                <tr class="hover:bg-gradient-to-l hover:from-primary-50 hover:to-blue-50 dark:hover:from-gray-700/50 dark:hover:to-gray-600/50 transition-all duration-300 group">
                    <td class="px-8 py-6 whitespace-nowrap">
                        <div class="flex items-center justify-end">
                            <div class="mr-4 text-right">
                                <div class="text-sm font-semibold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300">{{ employee.full_name }}</div>
                                <div class="text-sm text-gray-500 dark:text-gray-400 font-mono">{{ employee.employee_id }}</div>
                            </div>
                            <div class="flex-shrink-0 h-12 w-12 relative">
                                <div class="h-12 w-12 rounded-full bg-gradient-to-br from-primary-500 to-primary-700 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                                    <span class="text-white font-bold text-lg">{{ employee.first_name.0 }}{{ employee.last_name.0 }}</span>
                                </div>
                                <div class="absolute -inset-1 bg-primary-500/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            </div>
                        </div>
                    </td>
                    <td class="px-8 py-6 whitespace-nowrap text-right">
                        <div class="text-sm font-medium text-gray-900 dark:text-white">{{ employee.position }}</div>
                    </td>
                    <td class="px-8 py-6 whitespace-nowrap text-right">
                        <div class="text-sm text-gray-900 dark:text-white">{{ employee.department|default:"-" }}</div>
                    </td>
                    <td class="px-8 py-6 whitespace-nowrap text-right">
                        <div class="text-sm font-semibold text-green-600 dark:text-green-400">${{ employee.monthly_salary|floatformat:2 }}</div>
                    </td>
                    <td class="px-8 py-6 whitespace-nowrap text-right">
                        <div class="text-sm font-semibold text-blue-600 dark:text-blue-400">${{ employee.manager_payment|floatformat:2 }}</div>
                    </td>
                    <td class="px-8 py-6 whitespace-nowrap">
                        <span class="inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full shadow-sm
                                   {% if employee.employment_status == 'active' %}bg-gradient-to-r from-green-100 to-green-200 text-green-800 dark:from-green-800 dark:to-green-700 dark:text-green-100
                                   {% elif employee.employment_status == 'inactive' %}bg-gradient-to-r from-yellow-100 to-yellow-200 text-yellow-800 dark:from-yellow-800 dark:to-yellow-700 dark:text-yellow-100
                                   {% elif employee.employment_status == 'terminated' %}bg-gradient-to-r from-red-100 to-red-200 text-red-800 dark:from-red-800 dark:to-red-700 dark:text-red-100
                                   {% else %}bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 dark:from-blue-800 dark:to-blue-700 dark:text-blue-100{% endif %}">
                            <div class="w-2 h-2 rounded-full mr-2
                                      {% if employee.employment_status == 'active' %}bg-green-500
                                      {% elif employee.employment_status == 'inactive' %}bg-yellow-500
                                      {% elif employee.employment_status == 'terminated' %}bg-red-500
                                      {% else %}bg-blue-500{% endif %}"></div>
                            {{ employee.get_employment_status_display }}
                        </span>
                    </td>
                    <td class="px-8 py-6 whitespace-nowrap text-sm font-medium">
                        <div class="flex space-x-3">
                            <a href="{% url 'employees:employee_detail' employee.pk %}" class="text-primary-600 hover:text-primary-800 dark:text-primary-400 dark:hover:text-primary-300 p-2 rounded-lg hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-all duration-300" title="View Details">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{% url 'employees:employee_update' employee.pk %}" class="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300 p-2 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-300" title="Edit Employee">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{% url 'employees:employee_delete' employee.pk %}" class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-300" title="Delete Employee">
                                <i class="fas fa-trash"></i>
                            </a>
                        </div>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="7" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                        {% if search_query %}
                            لم يتم العثور على موظفين يطابقون "{{ search_query }}".
                        {% else %}
                            لم يتم العثور على موظفين. <a href="{% url 'employees:employee_create' %}" class="text-blue-600 hover:text-blue-800 dark:text-blue-400">أضف الموظف الأول</a>.
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="px-8 py-6 border-t border-gray-200/50 dark:border-gray-700/50 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-b-2xl">
        <div class="flex justify-between items-center">
            <div class="text-sm text-gray-700 dark:text-gray-300 font-medium">
                <i class="fas fa-info-circle ml-2 text-primary-500"></i>
                عرض <span class="font-semibold text-primary-600 dark:text-primary-400">{{ page_obj.start_index }}</span> إلى
                <span class="font-semibold text-primary-600 dark:text-primary-400">{{ page_obj.end_index }}</span> من
                <span class="font-semibold text-primary-600 dark:text-primary-400">{{ page_obj.paginator.count }}</span> نتيجة
            </div>
            <div class="flex space-x-2">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}"
                       class="px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 bg-white dark:bg-gray-700 rounded-lg shadow hover:shadow-lg transition-all duration-300">
                        <i class="fas fa-angle-right ml-1"></i>السابق
                    </a>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <span class="px-4 py-2 text-sm bg-gradient-to-r from-primary-600 to-primary-700 text-white rounded-lg shadow-lg font-semibold">{{ num }}</span>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}"
                           class="px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 bg-white dark:bg-gray-700 rounded-lg shadow hover:shadow-lg transition-all duration-300">{{ num }}</a>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}"
                       class="px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 bg-white dark:bg-gray-700 rounded-lg shadow hover:shadow-lg transition-all duration-300">
                        التالي<i class="fas fa-angle-left mr-1"></i>
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Floating Action Button -->
<div class="fixed bottom-8 left-8 z-50">
    <a href="{% url 'employees:employee_create' %}" class="bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white w-16 h-16 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 flex items-center justify-center btn-glow pulse-glow group">
        <i class="fas fa-plus text-xl group-hover:scale-110 transition-transform duration-300"></i>
    </a>
</div>
{% endblock %}
