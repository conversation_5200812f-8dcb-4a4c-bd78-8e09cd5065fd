{% extends 'base.html' %}

{% block title %}خدمات الموظفين - نظام إدارة الموظفين{% endblock %}

{% block content %}
<div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg shadow-xl rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover-lift">
    <div class="px-8 py-6 border-b border-gray-200/50 dark:border-gray-700/50 bg-gradient-to-r from-primary-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-t-2xl">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <div class="relative">
                    <i class="fas fa-handshake text-3xl text-primary-600 dark:text-primary-400 float"></i>
                    <div class="absolute -inset-2 bg-primary-600/20 rounded-full blur opacity-50"></div>
                </div>
                <div>
                    <h1 class="text-3xl font-bold bg-gradient-to-r from-primary-600 to-primary-800 dark:from-primary-400 dark:to-primary-600 bg-clip-text text-transparent">
                        خدمات الموظفين
                    </h1>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">إدارة الخدمات المقدمة من الموظفين</p>
                </div>
            </div>
            <div class="text-right">
                <div class="text-3xl font-bold text-primary-600 dark:text-primary-400">{{ total_services }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">إجمالي الخدمات</div>
            </div>
        </div>
        
        <!-- Search Form -->
        <div class="mt-6">
            <form method="get" class="flex gap-4">
                <div class="flex-1 relative">
                    <div class="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400 dark:text-gray-500"></i>
                    </div>
                    <input type="text" name="search" value="{{ search_query }}"
                           placeholder="البحث بالموظف أو وصف الخدمة..."
                           class="w-full pr-12 pl-4 py-3 rounded-xl border-0 bg-white dark:bg-gray-700 shadow-lg focus:ring-2 focus:ring-primary-500 focus:shadow-glow transition-all duration-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400">
                </div>
                <button type="submit" class="bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 btn-glow">
                    <i class="fas fa-search ml-2"></i>بحث
                </button>
                <a href="{% url 'employees:service_create' %}" class="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 btn-glow">
                    <i class="fas fa-plus ml-2"></i>إضافة خدمة
                </a>
            </form>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="min-w-full">
            <thead class="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600">
                <tr>
                    <th class="px-8 py-4 text-right text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">الموظف</th>
                    <th class="px-8 py-4 text-right text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">تاريخ الخدمة</th>
                    <th class="px-8 py-4 text-right text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">وصف الخدمة</th>
                    <th class="px-8 py-4 text-right text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">المبلغ</th>
                    <th class="px-8 py-4 text-right text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">الإجراءات</th>
                </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {% for service in page_obj %}
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-300 group">
                    <td class="px-8 py-6 whitespace-nowrap text-right">
                        <div class="flex items-center justify-end group">
                            <div class="text-right">
                                <div class="text-sm font-medium text-gray-900 dark:text-white">{{ service.employee.full_name }}</div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">{{ service.employee.position }}</div>
                            </div>
                            <div class="relative mr-4">
                                <div class="h-12 w-12 rounded-full bg-gradient-to-r from-primary-500 to-primary-600 flex items-center justify-center text-white font-bold text-lg shadow-lg group-hover:scale-110 transition-transform duration-300">
                                    {{ service.employee.first_name|first }}{{ service.employee.last_name|first }}
                                </div>
                                <div class="absolute -inset-1 bg-primary-500/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            </div>
                        </div>
                    </td>
                    <td class="px-8 py-6 whitespace-nowrap text-right">
                        <div class="text-sm font-medium text-gray-900 dark:text-white">{{ service.service_date }}</div>
                    </td>
                    <td class="px-8 py-6 text-right">
                        <div class="text-sm text-gray-900 dark:text-white max-w-xs truncate" title="{{ service.service_description }}">
                            {{ service.service_description|truncatechars:50 }}
                        </div>
                    </td>
                    <td class="px-8 py-6 whitespace-nowrap text-right">
                        <div class="text-sm font-semibold text-green-600 dark:text-green-400">${{ service.service_amount|floatformat:2 }}</div>
                    </td>
                    <td class="px-8 py-6 whitespace-nowrap text-right">
                        <div class="flex items-center justify-end space-x-2 space-x-reverse">
                            <a href="{% url 'employees:service_detail' service.pk %}" class="text-primary-600 hover:text-primary-800 dark:text-primary-400 dark:hover:text-primary-300 p-2 rounded-lg hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-all duration-300" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{% url 'employees:service_update' service.pk %}" class="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300 p-2 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-300" title="تعديل الخدمة">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{% url 'employees:service_delete' service.pk %}" class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-300" title="حذف الخدمة">
                                <i class="fas fa-trash"></i>
                            </a>
                        </div>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="5" class="px-8 py-12 text-center">
                        <div class="flex flex-col items-center justify-center space-y-4">
                            <div class="relative">
                                <i class="fas fa-handshake text-6xl text-gray-300 dark:text-gray-600 float"></i>
                                <div class="absolute -inset-4 bg-gray-300/20 rounded-full blur opacity-50"></div>
                            </div>
                            <div class="text-xl font-medium text-gray-500 dark:text-gray-400">
                                {% if search_query %}
                                    لم يتم العثور على خدمات تطابق البحث "{{ search_query }}"
                                {% else %}
                                    لا توجد خدمات مسجلة حتى الآن
                                {% endif %}
                            </div>
                            <a href="{% url 'employees:service_create' %}" class="bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 btn-glow">
                                <i class="fas fa-plus ml-2"></i>إضافة خدمة جديدة
                            </a>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="px-8 py-6 border-t border-gray-200/50 dark:border-gray-700/50 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-b-2xl">
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-600 dark:text-gray-400">
                عرض {{ page_obj.start_index }} إلى {{ page_obj.end_index }} من أصل {{ page_obj.paginator.count }} خدمة
            </div>
            <div class="flex space-x-2 space-x-reverse">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" class="px-4 py-2 text-sm font-medium text-primary-600 dark:text-primary-400 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-300">
                        <i class="fas fa-chevron-right ml-1"></i>السابق
                    </a>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <span class="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-primary-600 rounded-lg">{{ num }}</span>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}" class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-300">{{ num }}</a>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" class="px-4 py-2 text-sm font-medium text-primary-600 dark:text-primary-400 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-300">
                        التالي<i class="fas fa-chevron-left mr-1"></i>
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
