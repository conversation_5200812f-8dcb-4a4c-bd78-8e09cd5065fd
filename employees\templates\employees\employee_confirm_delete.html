{% extends 'base.html' %}

{% block title %}Delete {{ employee.full_name }} - Employee Management System{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h1 class="text-2xl font-bold text-red-600 dark:text-red-400">
                <i class="fas fa-exclamation-triangle mr-2"></i>Confirm Delete
            </h1>
        </div>

        <div class="p-6">
            <div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-red-400"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                            Warning: This action cannot be undone
                        </h3>
                        <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                            <p>You are about to permanently delete this employee record. All associated data will be lost.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Employee Details</h3>
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Name</label>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ employee.full_name }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Employee ID</label>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white font-mono">{{ employee.employee_id }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Position</label>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ employee.position }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Department</label>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ employee.department|default:"-" }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Email</label>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ employee.email }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Monthly Salary</label>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white">${{ employee.monthly_salary|floatformat:2 }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <form method="post">
                {% csrf_token %}
                <div class="flex justify-end space-x-3">
                    <a href="{% url 'employees:employee_detail' employee.pk %}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                        <i class="fas fa-times mr-1"></i>Cancel
                    </a>
                    <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md">
                        <i class="fas fa-trash mr-1"></i>Yes, Delete Employee
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
