{% extends 'base.html' %}

{% block title %}{{ title }} - نظام إدارة الموظفين{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg shadow-2xl rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover-lift">
        <div class="px-8 py-6 border-b border-gray-200/50 dark:border-gray-700/50 bg-gradient-to-r from-primary-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-t-2xl">
            <div class="flex items-center space-x-4">
                <div class="relative">
                    <i class="fas fa-handshake text-3xl text-primary-600 dark:text-primary-400 float"></i>
                    <div class="absolute -inset-2 bg-primary-600/20 rounded-full blur opacity-50"></div>
                </div>
                <div>
                    <h1 class="text-3xl font-bold bg-gradient-to-r from-primary-600 to-primary-800 dark:from-primary-400 dark:to-primary-600 bg-clip-text text-transparent">
                        {{ title }}
                    </h1>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">املأ معلومات الخدمة أدناه</p>
                </div>
            </div>
        </div>

        <form method="post" class="p-8">
            {% csrf_token %}

            {% if form.non_field_errors %}
                <div class="mb-6 p-6 bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/50 dark:to-red-800/50 border border-red-200 dark:border-red-700 text-red-800 dark:text-red-200 rounded-xl shadow-lg backdrop-blur-sm animate-slide-down">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle mr-3 text-lg"></i>
                        <div>{{ form.non_field_errors }}</div>
                    </div>
                </div>
            {% endif %}

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- Employee Selection -->
                <div class="space-y-2">
                    <label for="{{ form.employee.id_for_label }}" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center">
                        <i class="fas fa-user ml-2 text-primary-600 dark:text-primary-400"></i>
                        {{ form.employee.label }}
                        <span class="text-red-500 mr-1">*</span>
                    </label>
                    {{ form.employee }}
                    {% if form.employee.errors %}
                        <div class="text-red-600 dark:text-red-400 text-sm mt-1 flex items-center">
                            <i class="fas fa-exclamation-circle ml-2"></i>
                            {{ form.employee.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <!-- Service Date -->
                <div class="space-y-2">
                    <label for="{{ form.service_date.id_for_label }}" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center">
                        <i class="fas fa-calendar ml-2 text-primary-600 dark:text-primary-400"></i>
                        {{ form.service_date.label }}
                        <span class="text-red-500 mr-1">*</span>
                    </label>
                    {{ form.service_date }}
                    {% if form.service_date.errors %}
                        <div class="text-red-600 dark:text-red-400 text-sm mt-1 flex items-center">
                            <i class="fas fa-exclamation-circle ml-2"></i>
                            {{ form.service_date.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <!-- Service Amount -->
                <div class="space-y-2">
                    <label for="{{ form.service_amount.id_for_label }}" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center">
                        <i class="fas fa-dollar-sign ml-2 text-primary-600 dark:text-primary-400"></i>
                        {{ form.service_amount.label }}
                        <span class="text-red-500 mr-1">*</span>
                    </label>
                    {{ form.service_amount }}
                    {% if form.service_amount.errors %}
                        <div class="text-red-600 dark:text-red-400 text-sm mt-1 flex items-center">
                            <i class="fas fa-exclamation-circle ml-2"></i>
                            {{ form.service_amount.errors.0 }}
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Service Description -->
            <div class="mt-8 space-y-2">
                <label for="{{ form.service_description.id_for_label }}" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center">
                    <i class="fas fa-file-alt ml-2 text-primary-600 dark:text-primary-400"></i>
                    {{ form.service_description.label }}
                    <span class="text-red-500 mr-1">*</span>
                </label>
                {{ form.service_description }}
                {% if form.service_description.errors %}
                    <div class="text-red-600 dark:text-red-400 text-sm mt-1 flex items-center">
                        <i class="fas fa-exclamation-circle ml-2"></i>
                        {{ form.service_description.errors.0 }}
                    </div>
                {% endif %}
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    <i class="fas fa-info-circle ml-1"></i>
                    اكتب وصفاً تفصيلياً للخدمة التي قدمها الموظف للشركة
                </p>
            </div>

            <div class="mt-12 flex justify-start space-x-4 space-x-reverse">
                <button type="submit" class="bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 btn-glow">
                    <i class="fas fa-save ml-2"></i>{{ submit_text }}
                </button>
                <a href="{% url 'employees:service_list' %}" class="bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
                    <i class="fas fa-times ml-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
