from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from .models import Employee
from .forms import EmployeeForm


def employee_list(request):
    """Display a list of all employees with search and pagination."""
    search_query = request.GET.get('search', '')
    employees = Employee.objects.all()

    if search_query:
        employees = employees.filter(
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(employee_id__icontains=search_query) |
            Q(position__icontains=search_query) |
            Q(department__icontains=search_query) |
            Q(email__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(employees, 10)  # Show 10 employees per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'total_employees': Employee.objects.count(),
    }
    return render(request, 'employees/employee_list.html', context)


def employee_detail(request, pk):
    """Display detailed information about a specific employee."""
    employee = get_object_or_404(Employee, pk=pk)
    context = {
        'employee': employee,
    }
    return render(request, 'employees/employee_detail.html', context)


def employee_create(request):
    """Create a new employee."""
    if request.method == 'POST':
        form = EmployeeForm(request.POST)
        if form.is_valid():
            employee = form.save()
            messages.success(request, f'تم إنشاء الموظف {employee.full_name} بنجاح.')
            return redirect('employees:employee_detail', pk=employee.pk)
    else:
        form = EmployeeForm()

    context = {
        'form': form,
        'title': 'إضافة موظف جديد',
        'submit_text': 'إنشاء موظف',
    }
    return render(request, 'employees/employee_form.html', context)


def employee_update(request, pk):
    """Update an existing employee."""
    employee = get_object_or_404(Employee, pk=pk)

    if request.method == 'POST':
        form = EmployeeForm(request.POST, instance=employee)
        if form.is_valid():
            employee = form.save()
            messages.success(request, f'تم تحديث الموظف {employee.full_name} بنجاح.')
            return redirect('employees:employee_detail', pk=employee.pk)
    else:
        form = EmployeeForm(instance=employee)

    context = {
        'form': form,
        'employee': employee,
        'title': f'تعديل {employee.full_name}',
        'submit_text': 'تحديث الموظف',
    }
    return render(request, 'employees/employee_form.html', context)


def employee_delete(request, pk):
    """Delete an employee."""
    employee = get_object_or_404(Employee, pk=pk)

    if request.method == 'POST':
        employee_name = employee.full_name
        employee.delete()
        messages.success(request, f'تم حذف الموظف {employee_name} بنجاح.')
        return redirect('employees:employee_list')

    context = {
        'employee': employee,
    }
    return render(request, 'employees/employee_confirm_delete.html', context)
