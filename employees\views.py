from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Sum
from .models import Employee, EmployeeService
from .forms import EmployeeForm, EmployeeServiceForm


def dashboard(request):
    """Display dashboard with overview of employees and services."""
    total_employees = Employee.objects.count()
    active_employees = Employee.objects.filter(employment_status='active').count()
    total_services = EmployeeService.objects.count()
    total_service_amount = EmployeeService.objects.aggregate(
        total=Sum('service_amount')
    )['total'] or 0

    context = {
        'total_employees': total_employees,
        'active_employees': active_employees,
        'total_services': total_services,
        'total_service_amount': total_service_amount,
    }
    return render(request, 'employees/dashboard.html', context)


def employee_list(request):
    """Display a list of all employees with search and pagination."""
    search_query = request.GET.get('search', '')
    employees = Employee.objects.all()

    if search_query:
        employees = employees.filter(
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(employee_id__icontains=search_query) |
            Q(position__icontains=search_query) |
            Q(department__icontains=search_query) |
            Q(email__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(employees, 10)  # Show 10 employees per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'total_employees': Employee.objects.count(),
    }
    return render(request, 'employees/employee_list.html', context)


def employee_detail(request, pk):
    """Display detailed information about a specific employee."""
    employee = get_object_or_404(Employee, pk=pk)
    context = {
        'employee': employee,
    }
    return render(request, 'employees/employee_detail.html', context)


def employee_create(request):
    """Create a new employee."""
    if request.method == 'POST':
        form = EmployeeForm(request.POST)
        if form.is_valid():
            employee = form.save()
            messages.success(request, f'تم إنشاء الموظف {employee.full_name} بنجاح.')
            return redirect('employees:employee_detail', pk=employee.pk)
    else:
        form = EmployeeForm()

    context = {
        'form': form,
        'title': 'إضافة موظف جديد',
        'submit_text': 'إنشاء موظف',
    }
    return render(request, 'employees/employee_form.html', context)


def employee_update(request, pk):
    """Update an existing employee."""
    employee = get_object_or_404(Employee, pk=pk)

    if request.method == 'POST':
        form = EmployeeForm(request.POST, instance=employee)
        if form.is_valid():
            employee = form.save()
            messages.success(request, f'تم تحديث الموظف {employee.full_name} بنجاح.')
            return redirect('employees:employee_detail', pk=employee.pk)
    else:
        form = EmployeeForm(instance=employee)

    context = {
        'form': form,
        'employee': employee,
        'title': f'تعديل {employee.full_name}',
        'submit_text': 'تحديث الموظف',
    }
    return render(request, 'employees/employee_form.html', context)


def employee_delete(request, pk):
    """Delete an employee."""
    employee = get_object_or_404(Employee, pk=pk)

    if request.method == 'POST':
        employee_name = employee.full_name
        employee.delete()
        messages.success(request, f'تم حذف الموظف {employee_name} بنجاح.')
        return redirect('employees:employee_list')

    context = {
        'employee': employee,
    }
    return render(request, 'employees/employee_confirm_delete.html', context)


# Employee Services Views
def service_list(request):
    """Display a list of all employee services with search and pagination."""
    search_query = request.GET.get('search', '')
    services = EmployeeService.objects.select_related('employee').all()

    if search_query:
        services = services.filter(
            Q(employee__first_name__icontains=search_query) |
            Q(employee__last_name__icontains=search_query) |
            Q(service_description__icontains=search_query) |
            Q(employee__employee_id__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(services, 10)  # Show 10 services per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'total_services': EmployeeService.objects.count(),
    }
    return render(request, 'employees/service_list.html', context)


def service_detail(request, pk):
    """Display detailed information about a specific service."""
    service = get_object_or_404(EmployeeService, pk=pk)
    context = {
        'service': service,
    }
    return render(request, 'employees/service_detail.html', context)


def service_create(request):
    """Create a new employee service."""
    if request.method == 'POST':
        form = EmployeeServiceForm(request.POST)
        if form.is_valid():
            service = form.save()
            messages.success(request, f'تم إنشاء خدمة للموظف {service.employee.full_name} بنجاح.')
            return redirect('employees:service_detail', pk=service.pk)
    else:
        form = EmployeeServiceForm()

    context = {
        'form': form,
        'title': 'إضافة خدمة جديدة',
        'submit_text': 'إنشاء خدمة',
    }
    return render(request, 'employees/service_form.html', context)


def service_update(request, pk):
    """Update an existing employee service."""
    service = get_object_or_404(EmployeeService, pk=pk)

    if request.method == 'POST':
        form = EmployeeServiceForm(request.POST, instance=service)
        if form.is_valid():
            service = form.save()
            messages.success(request, f'تم تحديث خدمة الموظف {service.employee.full_name} بنجاح.')
            return redirect('employees:service_detail', pk=service.pk)
    else:
        form = EmployeeServiceForm(instance=service)

    context = {
        'form': form,
        'service': service,
        'title': f'تعديل خدمة {service.employee.full_name}',
        'submit_text': 'تحديث الخدمة',
    }
    return render(request, 'employees/service_form.html', context)


def service_delete(request, pk):
    """Delete an employee service."""
    service = get_object_or_404(EmployeeService, pk=pk)

    if request.method == 'POST':
        employee_name = service.employee.full_name
        service.delete()
        messages.success(request, f'تم حذف خدمة الموظف {employee_name} بنجاح.')
        return redirect('employees:service_list')

    context = {
        'service': service,
    }
    return render(request, 'employees/service_confirm_delete.html', context)
