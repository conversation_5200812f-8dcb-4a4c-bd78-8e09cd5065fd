from django.db import models
from django.core.validators import MinValueValidator
from decimal import Decimal


class Employee(models.Model):
    # Personal Information
    first_name = models.CharField(max_length=100, verbose_name="First Name")
    last_name = models.CharField(max_length=100, verbose_name="Last Name")
    email = models.EmailField(unique=True, verbose_name="Email Address")
    phone_number = models.CharField(max_length=20, blank=True, verbose_name="Phone Number")
    date_of_birth = models.DateField(null=True, blank=True, verbose_name="Date of Birth")

    # Employment Information
    employee_id = models.Char<PERSON>ield(max_length=20, unique=True, verbose_name="Employee ID")
    position = models.CharField(max_length=100, verbose_name="Position/Job Title")
    department = models.CharField(max_length=100, blank=True, verbose_name="Department")
    hire_date = models.DateField(verbose_name="Hire Date")

    # Address Information
    address = models.TextField(blank=True, verbose_name="Address")
    city = models.CharField(max_length=100, blank=True, verbose_name="City")
    state = models.CharField(max_length=100, blank=True, verbose_name="State/Province")
    postal_code = models.CharField(max_length=20, blank=True, verbose_name="Postal Code")
    country = models.CharField(max_length=100, blank=True, verbose_name="Country")

    # Salary Information
    monthly_salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        verbose_name="Monthly Salary"
    )
    manager_payment = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        verbose_name="Manager Payment Amount",
        help_text="Amount that the manager must pay the employee"
    )

    # Employment Status
    EMPLOYMENT_STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('terminated', 'Terminated'),
        ('on_leave', 'On Leave'),
    ]
    employment_status = models.CharField(
        max_length=20,
        choices=EMPLOYMENT_STATUS_CHOICES,
        default='active',
        verbose_name="Employment Status"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['last_name', 'first_name']
        verbose_name = "Employee"
        verbose_name_plural = "Employees"

    def __str__(self):
        return f"{self.first_name} {self.last_name} - {self.position}"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def annual_salary(self):
        return self.monthly_salary * 12

    @property
    def annual_manager_payment(self):
        return self.manager_payment * 12


class EmployeeService(models.Model):
    """Model to track services provided by employees to the company."""

    # Employee who provided the service
    employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='services',
        verbose_name="الموظف"
    )

    # Service date
    service_date = models.DateField(verbose_name="تاريخ الخدمة")

    # Service description
    service_description = models.TextField(
        verbose_name="وصف الخدمة",
        help_text="وصف تفصيلي للخدمة التي قدمها الموظف للشركة"
    )

    # Service amount
    service_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        verbose_name="مبلغ الخدمة",
        help_text="المبلغ المالي للخدمة المقدمة"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-service_date', '-created_at']
        verbose_name = "خدمة موظف"
        verbose_name_plural = "خدمات الموظفين"

    def __str__(self):
        return f"{self.employee.full_name} - {self.service_date} - ${self.service_amount}"
