{% load static tailwind_tags %}
<!DOCTYPE html>
<html lang="en" class="dark">
	<head>
		<title>{% block title %}Employee Management System{% endblock %}</title>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
		<script src="https://cdn.tailwindcss.com"></script>
		<script>
			tailwind.config = {
				darkMode: 'class',
				theme: {
					extend: {
						colors: {
							dark: {
								50: '#f8fafc',
								100: '#f1f5f9',
								200: '#e2e8f0',
								300: '#cbd5e1',
								400: '#94a3b8',
								500: '#64748b',
								600: '#475569',
								700: '#334155',
								800: '#1e293b',
								900: '#0f172a',
							}
						}
					}
				}
			}
		</script>
		<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
	</head>

	<body class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100 font-sans leading-normal tracking-normal min-h-screen">
		<!-- Navigation -->
		<nav class="bg-white dark:bg-gray-800 shadow-lg">
			<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div class="flex justify-between h-16">
					<div class="flex items-center">
						<a href="{% url 'employees:employee_list' %}" class="flex items-center space-x-2">
							<i class="fas fa-users text-blue-600 dark:text-blue-400 text-2xl"></i>
							<span class="text-xl font-bold text-gray-900 dark:text-white">Employee Management</span>
						</a>
					</div>
					<div class="flex items-center space-x-4">
						<a href="{% url 'employees:employee_list' %}" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium">
							<i class="fas fa-list mr-1"></i>All Employees
						</a>
						<a href="{% url 'employees:employee_create' %}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
							<i class="fas fa-plus mr-1"></i>Add Employee
						</a>
					</div>
				</div>
			</div>
		</nav>

		<!-- Main Content -->
		<main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
			{% if messages %}
				<div class="mb-4">
					{% for message in messages %}
						<div class="alert alert-{{ message.tags }} bg-{% if message.tags == 'error' %}red{% elif message.tags == 'success' %}green{% elif message.tags == 'warning' %}yellow{% else %}blue{% endif %}-100 dark:bg-{% if message.tags == 'error' %}red{% elif message.tags == 'success' %}green{% elif message.tags == 'warning' %}yellow{% else %}blue{% endif %}-900 border border-{% if message.tags == 'error' %}red{% elif message.tags == 'success' %}green{% elif message.tags == 'warning' %}yellow{% else %}blue{% endif %}-400 text-{% if message.tags == 'error' %}red{% elif message.tags == 'success' %}green{% elif message.tags == 'warning' %}yellow{% else %}blue{% endif %}-700 dark:text-{% if message.tags == 'error' %}red{% elif message.tags == 'success' %}green{% elif message.tags == 'warning' %}yellow{% else %}blue{% endif %}-200 px-4 py-3 rounded relative" role="alert">
							<span class="block sm:inline">{{ message }}</span>
						</div>
					{% endfor %}
				</div>
			{% endif %}

			{% block content %}
			{% endblock %}
		</main>

		<!-- Footer -->
		<footer class="bg-white dark:bg-gray-800 shadow-lg mt-auto">
			<div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
				<div class="text-center text-gray-600 dark:text-gray-400">
					<p>&copy; 2025 Employee Management System. Built with Django & TailwindCSS.</p>
				</div>
			</div>
		</footer>
	</body>
</html>
