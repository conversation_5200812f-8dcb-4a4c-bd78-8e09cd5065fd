{% extends 'base.html' %}

{% block title %}لوحة التحكم - نظام إدارة الموظفين{% endblock %}

{% block content %}
<div class="space-y-8">
    <!-- Welcome Section -->
    <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg shadow-xl rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover-lift">
        <div class="px-8 py-6 bg-gradient-to-r from-primary-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-t-2xl">
            <div class="text-center">
                <div class="relative inline-block">
                    <i class="fas fa-tachometer-alt text-4xl text-primary-600 dark:text-primary-400 float"></i>
                    <div class="absolute -inset-4 bg-primary-600/20 rounded-full blur opacity-50"></div>
                </div>
                <h1 class="mt-4 text-4xl font-bold bg-gradient-to-r from-primary-600 to-primary-800 dark:from-primary-400 dark:to-primary-600 bg-clip-text text-transparent">
                    مرحباً بك في نظام إدارة الموظفين
                </h1>
                <p class="text-lg text-gray-600 dark:text-gray-400 mt-2">اختر القسم الذي تريد الوصول إليه</p>
            </div>
        </div>
    </div>

    <!-- Main Navigation Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <!-- Employees Section -->
        <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg shadow-xl rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover-lift group">
            <div class="p-8">
                <div class="text-center">
                    <div class="relative inline-block">
                        <div class="h-20 w-20 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white shadow-lg mx-auto group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-users text-3xl"></i>
                        </div>
                        <div class="absolute -inset-2 bg-blue-500/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                    
                    <h2 class="mt-6 text-2xl font-bold text-gray-900 dark:text-white">إدارة الموظفين</h2>
                    <p class="text-gray-600 dark:text-gray-400 mt-2 mb-6">عرض وإدارة بيانات الموظفين</p>
                    
                    <div class="space-y-3">
                        <a href="{% url 'employees:employee_list' %}" class="block w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 btn-glow">
                            <i class="fas fa-list ml-2"></i>عرض جميع الموظفين
                        </a>
                        <a href="{% url 'employees:employee_create' %}" class="block w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 btn-glow">
                            <i class="fas fa-plus ml-2"></i>إضافة موظف جديد
                        </a>
                    </div>
                    
                    <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-600">
                        <div class="flex justify-center space-x-6 space-x-reverse text-sm text-gray-600 dark:text-gray-400">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ total_employees }}</div>
                                <div>إجمالي الموظفين</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ active_employees }}</div>
                                <div>موظف نشط</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Employee Services Section -->
        <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg shadow-xl rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover-lift group">
            <div class="p-8">
                <div class="text-center">
                    <div class="relative inline-block">
                        <div class="h-20 w-20 rounded-full bg-gradient-to-r from-purple-500 to-purple-600 flex items-center justify-center text-white shadow-lg mx-auto group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-handshake text-3xl"></i>
                        </div>
                        <div class="absolute -inset-2 bg-purple-500/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                    
                    <h2 class="mt-6 text-2xl font-bold text-gray-900 dark:text-white">خدمات الموظفين</h2>
                    <p class="text-gray-600 dark:text-gray-400 mt-2 mb-6">إدارة الخدمات المقدمة من الموظفين</p>
                    
                    <div class="space-y-3">
                        <a href="{% url 'employees:service_list' %}" class="block w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 btn-glow">
                            <i class="fas fa-list ml-2"></i>عرض جميع الخدمات
                        </a>
                        <a href="{% url 'employees:service_create' %}" class="block w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 btn-glow">
                            <i class="fas fa-plus ml-2"></i>إضافة خدمة جديدة
                        </a>
                    </div>
                    
                    <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-600">
                        <div class="flex justify-center space-x-6 space-x-reverse text-sm text-gray-600 dark:text-gray-400">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ total_services }}</div>
                                <div>إجمالي الخدمات</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600 dark:text-green-400">${{ total_service_amount|floatformat:0 }}</div>
                                <div>إجمالي المبالغ</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg shadow-xl rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover-lift">
        <div class="px-8 py-6 border-b border-gray-200/50 dark:border-gray-700/50 bg-gradient-to-r from-primary-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-t-2xl">
            <h3 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                <i class="fas fa-chart-bar ml-2 text-primary-600 dark:text-primary-400"></i>
                إحصائيات سريعة
            </h3>
        </div>
        <div class="p-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ total_employees }}</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">إجمالي الموظفين</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-green-600 dark:text-green-400">{{ active_employees }}</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">موظف نشط</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-purple-600 dark:text-purple-400">{{ total_services }}</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">إجمالي الخدمات</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-orange-600 dark:text-orange-400">${{ total_service_amount|floatformat:0 }}</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">إجمالي قيمة الخدمات</div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
