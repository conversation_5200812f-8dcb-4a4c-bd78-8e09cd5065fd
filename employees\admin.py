from django.contrib import admin
from .models import Employee


@admin.register(Employee)
class EmployeeAdmin(admin.ModelAdmin):
    list_display = ['employee_id', 'full_name', 'position', 'department', 'monthly_salary', 'employment_status', 'hire_date']
    list_filter = ['employment_status', 'department', 'hire_date']
    search_fields = ['first_name', 'last_name', 'employee_id', 'email', 'position']
    ordering = ['last_name', 'first_name']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Personal Information', {
            'fields': ('first_name', 'last_name', 'email', 'phone_number', 'date_of_birth')
        }),
        ('Employment Information', {
            'fields': ('employee_id', 'position', 'department', 'hire_date', 'employment_status')
        }),
        ('Address Information', {
            'fields': ('address', 'city', 'state', 'postal_code', 'country'),
            'classes': ('collapse',)
        }),
        ('Salary Information', {
            'fields': ('monthly_salary', 'manager_payment')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
