# Generated by Django 4.2.7 on 2025-07-31 19:02

from decimal import Decimal
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('employees', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmployeeService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('service_date', models.DateField(verbose_name='تاريخ الخدمة')),
                ('service_description', models.TextField(help_text='وصف تفصيلي للخدمة التي قدمها الموظف للشركة', verbose_name='وصف الخدمة')),
                ('service_amount', models.DecimalField(decimal_places=2, help_text='المبلغ المالي للخدمة المقدمة', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='مبلغ الخدمة')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='services', to='employees.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'خدمة موظف',
                'verbose_name_plural': 'خدمات الموظفين',
                'ordering': ['-service_date', '-created_at'],
            },
        ),
    ]
